#include "dpi_cjson.h"
#include "dpi_common.h"
#include "dpi_detect.h"
#include "dpi_log.h"
#include "dpi_pint.h"
#include "dpi_utils.h"
#include "wxcs_def.h"
#include <ctype.h>
#include <glib.h>
#include <pthread.h>
#include <stdbool.h>
#include <stdlib.h>
#include <unistd.h>

extern struct global_config g_config;

// 全局变量
static wxc_handle       g_tencent_meeting_handle = NULL;
static GHashTable      *g_tencent_meeting_login_hash = NULL;  // login表：IP -> LoginInfo
static GHashTable      *g_tencent_meeting_person_hash = NULL; // person表：IP -> PersonInfo
static pthread_rwlock_t g_tencent_meeting_rwlock = PTHREAD_RWLOCK_INITIALIZER;
static pthread_t        g_tencent_meeting_thread;

// 登录信息结构体 - 用于login_table，以IP为key
typedef struct {
  char     loginID[19];        // 个人常规账号
  char     uid[19];            // 个人UID
  uint32_t lastActiveTime;     // 最后活跃时间
  uint8_t  hasLoginID;         // 是否有loginID
  uint8_t  hasUID;             // 是否有UID
} TencentMeetingLoginInfo;

// 会话信息结构体 - 用于person_table，以IP或IMSI为key，用于聚合会话
typedef struct {
  ST_TencentMeeting meeting_info;    // 完整的腾讯会议信息
  uint32_t          lastActiveTime;  // 最后活跃时间
  uint8_t           isTimeout;       // 是否超时
} TencentMeetingPersonInfo;

// JSON字符串清理函数
static char *clean_json_string(const char *raw_json) {
  if (!raw_json)
    return NULL;

  size_t len = strlen(raw_json);
  char  *cleaned = (char *)malloc(len * 2 + 1);  // 预留足够空间
  if (!cleaned)
    return NULL;

  size_t write_pos = 0;
  bool   in_string = false;
  bool   escape_next = false;

  for (size_t i = 0; i < len; i++) {
    char c = raw_json[i];

    // 跳过非打印字符（除了必要的JSON字符）
    if (!in_string && (c < 32 || c > 126)) {
      if (c != '\n' && c != '\r' && c != '\t') {
        continue;
      }
    }

    if (escape_next) {
      // 处理转义字符
      cleaned[write_pos++] = '\\';
      if (c == '"' || c == '\\' || c == '/' || c == 'b' || c == 'f' || c == 'n' || c == 'r' || c == 't') {
        cleaned[write_pos++] = c;
      } else if (c >= 32 && c <= 126) {
        cleaned[write_pos++] = c;
      }
      escape_next = false;
      continue;
    }

    if (c == '\\') {
      escape_next = true;
      continue;
    }

    if (c == '"' && !escape_next) {
      in_string = !in_string;
    }

    // 在字符串内部，过滤掉不可打印的二进制字符
    if (in_string && (c < 32 || c > 126)) {
      if (c == '\n') {
        cleaned[write_pos++] = '\\';
        cleaned[write_pos++] = 'n';
      } else if (c == '\r') {
        cleaned[write_pos++] = '\\';
        cleaned[write_pos++] = 'r';
      } else if (c == '\t') {
        cleaned[write_pos++] = '\\';
        cleaned[write_pos++] = 't';
      }
      // 其他不可打印字符直接跳过
      continue;
    }

    cleaned[write_pos++] = c;
  }

  cleaned[write_pos] = '\0';
  return cleaned;
}

// 修复JSON格式错误的函数
static char *fix_json_format(const char *json_str) {
  if (!json_str)
    return NULL;

  size_t len = strlen(json_str);
  char  *fixed = (char *)malloc(len * 2 + 1);
  if (!fixed)
    return NULL;

  size_t write_pos = 0;
  bool   in_string = false;
  bool   need_comma = false;

  for (size_t i = 0; i < len; i++) {
    char c = json_str[i];

    if (c == '"' && (i == 0 || json_str[i - 1] != '\\')) {
      in_string = !in_string;
      if (!in_string && need_comma) {
        // 字符串结束后，检查是否需要添加逗号
        size_t j = i + 1;
        while (j < len && (json_str[j] == ' ' || json_str[j] == '\n' || json_str[j] == '\r' || json_str[j] == '\t')) {
          j++;
        }
        if (j < len && json_str[j] == '"') {
          fixed[write_pos++] = c;
          fixed[write_pos++] = ',';
          need_comma = false;
          continue;
        }
      }
      need_comma = !in_string;
    }

    // 修复缺失的冒号
    if (!in_string && c == '"' && i + 1 < len) {
      size_t j = i + 1;
      while (j < len && json_str[j] != '"') j++;
      if (j < len) {
        j++;  // 跳过结束引号
        while (j < len && (json_str[j] == ' ' || json_str[j] == '\n' || json_str[j] == '\r' || json_str[j] == '\t')) {
          j++;
        }
        if (j < len && json_str[j] == '"' && (j == 0 || json_str[j - 1] != ':')) {
          // 在两个字符串之间缺少冒号
          fixed[write_pos++] = c;
          // 复制到下一个引号
          for (size_t k = i + 1; k <= j - 1; k++) {
            if (k < len)
              fixed[write_pos++] = json_str[k];
          }
          fixed[write_pos++] = ':';
          i = j - 1;
          continue;
        }
      }
    }

    fixed[write_pos++] = c;
  }

  fixed[write_pos] = '\0';
  return fixed;
}

// 专门处理protobuf转JSON后的格式问题
static char *fix_protobuf_json(const char *raw_json) {
  if (!raw_json)
    return NULL;

  // 示例输入: {\n\"1\":\"2\":\n\"6\":"\263\"}
  // 期望输出: {"1":"2","6":"value"}

  size_t len = strlen(raw_json);
  char  *result = (char *)malloc(len * 2 + 1);
  if (!result)
    return NULL;

  size_t write_pos = 0;
  bool   in_string = false;
  bool   after_colon = false;

  for (size_t i = 0; i < len; i++) {
    char c = raw_json[i];

    // 跳过换行符和回车符（除非在字符串内）
    if (!in_string && (c == '\n' || c == '\r')) {
      continue;
    }

    // 处理转义的引号
    if (c == '\\' && i + 1 < len && raw_json[i + 1] == '"') {
      result[write_pos++] = '"';
      i++;  // 跳过下一个字符
      if (!in_string) {
        in_string = true;
      } else {
        in_string = false;
        after_colon = false;
      }
      continue;
    }

    // 处理普通引号
    if (c == '"') {
      in_string = !in_string;
      result[write_pos++] = c;
      if (!in_string) {
        after_colon = false;
      }
      continue;
    }

    // 处理冒号
    if (!in_string && c == ':') {
      result[write_pos++] = c;
      after_colon = true;
      continue;
    }

    // 在字符串内部处理二进制字符
    if (in_string) {
      // 如果是不可打印字符，转换为十六进制表示或跳过
      if ((unsigned char)c < 32 || (unsigned char)c > 126) {
        if (c == '\t') {
          result[write_pos++] = '\\';
          result[write_pos++] = 't';
        } else if (c == '\n') {
          result[write_pos++] = '\\';
          result[write_pos++] = 'n';
        } else if (c == '\r') {
          result[write_pos++] = '\\';
          result[write_pos++] = 'r';
        } else {
          // 对于其他二进制字符，转换为十六进制或替换为安全字符
          result[write_pos++] = '?';  // 或者可以用sprintf转为\uXXXX格式
        }
      } else {
        result[write_pos++] = c;
      }
      continue;
    }

    // 检查是否需要在两个字符串值之间添加逗号
    if (!in_string && after_colon && c == '"') {
      // 这可能是一个新的键开始，需要先结束当前值
      result[write_pos++] = '"';
      result[write_pos++] = ',';
      after_colon = false;
      continue;
    }

    // 其他字符直接复制
    result[write_pos++] = c;
  }

  result[write_pos] = '\0';
  return result;
}

// 简单的字符串解析方法，用于从损坏的JSON中提取数值
static uint64_t extract_meeting_number_simple(const char *json_str) {
  if (!json_str)
    return 0;

  // 查找模式 "2": 后面的数字，或者 "3": 后面的数字
  const char *patterns[] = {"\"2\":", "\"3\":", NULL};

  for (int p = 0; patterns[p]; p++) {
    const char *pos = strstr(json_str, patterns[p]);
    if (pos) {
      pos += strlen(patterns[p]);

      // 跳过空白字符
      while (*pos && (*pos == ' ' || *pos == '\t' || *pos == '\n' || *pos == '\r')) {
        pos++;
      }

      // 查找数字
      if (*pos == '"') {
        pos++;  // 跳过引号
        char *end_quote = strchr(pos, '"');
        if (end_quote) {
          char   temp[32];
          size_t len = end_quote - pos;
          if (len < sizeof(temp)) {
            memcpy(temp, pos, len);
            temp[len] = '\0';
            return strtoull(temp, NULL, 10);
          }
        }
      } else if (isdigit(*pos)) {
        return strtoull(pos, NULL, 10);
      }
    }
  }

  return 0;
}

// 腾讯会议信息解析接口
typedef struct {
  uint64_t meetingNum;
  uint64_t sessionId;
  int      parseResult;  // 0: 失败, 1: 成功
} TencentMeetingInfo;

// protobuf解析函数声明
uint8_t decode_protobuf(char *input, int input_size, char **output);

// 抽象的腾讯会议信息解析接口
static int parse_tencent_meeting_info(
    const uint8_t *payload, uint32_t payload_len, uint32_t info_offset, uint32_t info_len, TencentMeetingInfo *meeting_info) {
  if (!payload || !meeting_info || info_len == 0) {
    return 0;
  }

  memset(meeting_info, 0, sizeof(TencentMeetingInfo));

  // 解析protobuf格式的info部分
  char   *json_output = NULL;
  uint8_t decode_result = decode_protobuf((char *)(payload + info_offset), info_len, &json_output);

  if (decode_result && json_output) {
    // 使用专门的protobuf JSON修复函数
    char *fixed_json = fix_protobuf_json(json_output);
    if (fixed_json) {
      DPI_LOG(DPI_LOG_DEBUG, "Original JSON: %s", json_output);
      DPI_LOG(DPI_LOG_DEBUG, "Fixed JSON: %s", fixed_json);

      cJSON *json = dpi_cjson_parse_json(fixed_json);
      if (json) {
        // 获取个人会议账号 {"2":{"3": xxxxxxxxxxxx}}
        cJSON *node2 = dpi_cjson_get_object_field(json, "2");
        if (node2) {
          cJSON *node3 = dpi_cjson_get_object_field(node2, "3");
          if (node3 && cJSON_IsNumber(node3)) {
            meeting_info->meetingNum = (uint64_t)node3->valuedouble;
            meeting_info->parseResult = 1;
          }
        }
        dpi_cjson_free_json(json);
      } else {
        // JSON解析仍然失败，尝试其他方法
        DPI_LOG(DPI_LOG_DEBUG, "JSON parse failed, trying alternative cleanup");
        char *alt_cleaned = clean_json_string(json_output);
        if (alt_cleaned) {
          char *alt_fixed = fix_json_format(alt_cleaned);
          if (alt_fixed) {
            cJSON *alt_json = dpi_cjson_parse_json(alt_fixed);
            if (alt_json) {
              cJSON *node2 = dpi_cjson_get_object_field(alt_json, "2");
              if (node2) {
                cJSON *node3 = dpi_cjson_get_object_field(node2, "3");
                if (node3 && cJSON_IsNumber(node3)) {
                  meeting_info->meetingNum = (uint64_t)node3->valuedouble;
                  meeting_info->parseResult = 1;
                }
              }
              dpi_cjson_free_json(alt_json);
            }
            free(alt_fixed);
          }
          free(alt_cleaned);
        }

        // 如果所有JSON解析方法都失败，使用简单字符串解析
        if (meeting_info->parseResult == 0) {
          uint64_t simple_result = extract_meeting_number_simple(json_output);
          if (simple_result > 0) {
            meeting_info->meetingNum = simple_result;
            meeting_info->parseResult = 1;
            DPI_LOG(DPI_LOG_DEBUG, "Used simple parsing, extracted: %lu", simple_result);
          }
        }
      }
      free(fixed_json);
    } else {
      // 如果修复失败，直接尝试简单解析
      uint64_t simple_result = extract_meeting_number_simple(json_output);
      if (simple_result > 0) {
        meeting_info->meetingNum = simple_result;
        meeting_info->parseResult = 1;
        DPI_LOG(DPI_LOG_DEBUG, "Used fallback simple parsing, extracted: %lu", simple_result);
      }
    }
    free(json_output);
  }

  return meeting_info->parseResult;
}

typedef struct Tencent_meeting_session_t {
  int      FlagPacketC2S;
  int      FlagPacketS2C;
  int      SessionType;
  uint64_t sessionId;
  char     selfMeetingNum[19];
  char     selfLoginNum[19];
  char     selfUID[19];
  uint32_t firstActiveTime;
  uint32_t lastActiveTime;
  uint32_t c2sPackCount;
  uint32_t c2sByteCount;
  uint32_t s2cPackCount;
  uint32_t s2cByteCount;
} Tencent_meeting_session;

// 初始化全局hash表
static void init_tencent_meeting_hash(void) {
  pthread_rwlock_wrlock(&g_tencent_meeting_rwlock);

  // 初始化login表
  if (!g_tencent_meeting_login_hash) {
    g_tencent_meeting_login_hash = g_hash_table_new_full(g_str_hash, g_str_equal, g_free, g_free);
    if (!g_tencent_meeting_login_hash) {
      DPI_LOG(DPI_LOG_ERROR, "error on create tencent meeting login hash");
      exit(-1);
    }
  }

  // 初始化person表
  if (!g_tencent_meeting_person_hash) {
    g_tencent_meeting_person_hash = g_hash_table_new_full(g_str_hash, g_str_equal, g_free, g_free);
    if (!g_tencent_meeting_person_hash) {
      DPI_LOG(DPI_LOG_ERROR, "error on create tencent meeting person hash");
      exit(-1);
    }
  }

  pthread_rwlock_unlock(&g_tencent_meeting_rwlock);
}

// 生成用户唯一标识key (优先使用MSISDN，否则使用IP)
static char *generate_user_key(struct flow_info *flow, uint8_t direction) {
  // 解析trailer获取MSISDN
  ST_trailer tmp_trailer;
  memset(&tmp_trailer, 0, sizeof(ST_trailer));
  dpi_TrailerParser(&tmp_trailer, (const char *)flow->trailer, flow->trailerlen, g_config.trailertype);

  char msisdn[16] = {0};
  snprintf(msisdn, sizeof(msisdn), "%lu", tmp_trailer.MSISDN);

  char ip[32] = {0};
  if (direction) {
    get_ipstring(flow->tuple.inner.ip_version, (char *)&flow->tuple.inner.ip_dst, ip, sizeof(ip));

  } else {
    get_ipstring(flow->tuple.inner.ip_version, (char *)&flow->tuple.inner.ip_src, ip, sizeof(ip));
  }

  if (msisdn[0] != '0') {
    return g_strdup(msisdn);
  } else if (ip[0] != '\0') {
    return g_strdup(ip);
  }
  return NULL;
}

// 查找登录信息 (线程安全)
static TencentMeetingLoginInfo *find_login_by_ip(char *ip) {
  if (!ip)
    return NULL;

  TencentMeetingLoginInfo *login_info = NULL;
  pthread_rwlock_rdlock(&g_tencent_meeting_rwlock);
  if (g_tencent_meeting_login_hash) {
    login_info = (TencentMeetingLoginInfo *)g_hash_table_lookup(g_tencent_meeting_login_hash, (gconstpointer)ip);
  }
  pthread_rwlock_unlock(&g_tencent_meeting_rwlock);
  return login_info;
}

// 查找会话信息 (线程安全)
static TencentMeetingPersonInfo *find_person_by_key(char *key) {
  if (!key)
    return NULL;

  TencentMeetingPersonInfo *person_info = NULL;
  pthread_rwlock_rdlock(&g_tencent_meeting_rwlock);
  if (g_tencent_meeting_person_hash) {
    person_info = (TencentMeetingPersonInfo *)g_hash_table_lookup(g_tencent_meeting_person_hash, (gconstpointer)key);
  }
  pthread_rwlock_unlock(&g_tencent_meeting_rwlock);
  return person_info;
}

// 查找登录信息 (线程不安全，需要在锁保护下调用)
static TencentMeetingLoginInfo *find_login_by_ip_unsafe(char *ip) {
  if (!ip || !g_tencent_meeting_login_hash)
    return NULL;
  return (TencentMeetingLoginInfo *)g_hash_table_lookup(g_tencent_meeting_login_hash, (gconstpointer)ip);
}

// 查找会话信息 (线程不安全，需要在锁保护下调用)
static TencentMeetingPersonInfo *find_person_by_key_unsafe(char *key) {
  if (!key || !g_tencent_meeting_person_hash)
    return NULL;
  return (TencentMeetingPersonInfo *)g_hash_table_lookup(g_tencent_meeting_person_hash, (gconstpointer)key);
}

// 创建新的会话信息
static TencentMeetingPersonInfo *create_new_person(struct flow_info *flow, const char *key, uint8_t direction) {
  TencentMeetingPersonInfo *person = (TencentMeetingPersonInfo *)calloc(1, sizeof(TencentMeetingPersonInfo));
  if (!person)
    return NULL;

  uint32_t now = g_config.g_now_time;

  // 设置trailer信息
  dpi_TrailerParser(&person->meeting_info.trailer, (const char *)flow->trailer, flow->trailerlen, g_config.trailertype);
  dpi_TrailerGetMAC(&person->meeting_info.trailer, (const char *)flow->ethhdr, g_config.RT_model);
  dpi_TrailerGetHWZZMAC(&person->meeting_info.trailer, (const char *)flow->ethhdr);
  dpi_TrailerSetDev(&person->meeting_info.trailer, g_config.devname);
  dpi_TrailerSetOpt(&person->meeting_info.trailer, g_config.operator_name);
  dpi_TrailerSetArea(&person->meeting_info.trailer, g_config.devArea);
  dpi_TrailerUpdateTS(&person->meeting_info.trailer);

  // 设置IP信息
  person->meeting_info.ip_version = flow->tuple.inner.ip_version;
  if (direction) {  //s2c
    if (flow->tuple.inner.ip_version == 4) {
      person->meeting_info.srcIp = flow->tuple.inner.ip_dst.ip4;
      person->meeting_info.dstIp = flow->tuple.inner.ip_src.ip4;
    } else {
      memcpy(person->meeting_info.srcIp6, flow->tuple.inner.ip_dst.ip6, 16);
      memcpy(person->meeting_info.dstIp6, flow->tuple.inner.ip_src.ip6, 16);
    }
    person->meeting_info.srcPort = ntohs(flow->tuple.inner.port_dst);
    person->meeting_info.dstPort = ntohs(flow->tuple.inner.port_src);
  } else {  //c2s
    if (flow->tuple.inner.ip_version == 4) {
      person->meeting_info.srcIp = flow->tuple.inner.ip_src.ip4;
      person->meeting_info.dstIp = flow->tuple.inner.ip_dst.ip4;
    } else {
      memcpy(person->meeting_info.srcIp6, flow->tuple.inner.ip_src.ip6, 16);
      memcpy(person->meeting_info.dstIp6, flow->tuple.inner.ip_dst.ip6, 16);
    }
    person->meeting_info.srcPort = ntohs(flow->tuple.inner.port_src);
    person->meeting_info.dstPort = ntohs(flow->tuple.inner.port_dst);
  }

  // 设置时间信息
  person->meeting_info.firstActiveTime = now;
  person->meeting_info.lastActiveTime = now;
  person->lastActiveTime = now;
  person->isTimeout = 0;

  return person;
}

// 生成IP key (优先使用MSISDN，否则使用IP)
static char *generate_ip_key(struct flow_info *flow, uint8_t direction) {
  char ip[32] = {0};
  if (direction) {
    get_ipstring(flow->tuple.inner.ip_version, (char *)&flow->tuple.inner.ip_dst, ip, sizeof(ip));
  } else {
    get_ipstring(flow->tuple.inner.ip_version, (char *)&flow->tuple.inner.ip_src, ip, sizeof(ip));
  }

  if (ip[0] != '\0') {
    return g_strdup(ip);
  }
  return NULL;
}

// 更新或创建登录信息 - 个人常规账号
static void update_user_login_num(struct flow_info *flow, const char *selfLoginNum, uint8_t direction) {
  if (!selfLoginNum || selfLoginNum[0] == '\0')
    return;

  char *ip_key = generate_ip_key(flow, direction);
  if (!ip_key)
    return;

  uint32_t now = g_config.g_now_time;
  TencentMeetingLoginInfo *new_login = NULL;

  // 第一阶段：查找登录信息，如果不存在则预创建（在锁外）
  TencentMeetingLoginInfo *login_info = find_login_by_ip(ip_key);

  if (!login_info) {
    // 在锁外预创建新登录信息
    new_login = (TencentMeetingLoginInfo *)calloc(1, sizeof(TencentMeetingLoginInfo));
    if (!new_login) {
      g_free(ip_key);
      return;
    }

    // 设置个人常规账号信息
    strncpy(new_login->loginID, selfLoginNum, sizeof(new_login->loginID) - 1);
    new_login->loginID[sizeof(new_login->loginID) - 1] = '\0';
    new_login->hasLoginID = 1;
    new_login->hasUID = 0;
    new_login->lastActiveTime = now;
  }

  // 第二阶段：最小化写锁，只做插入或更新操作
  pthread_rwlock_wrlock(&g_tencent_meeting_rwlock);
  if (g_tencent_meeting_login_hash) {
    login_info = find_login_by_ip_unsafe(ip_key);

    if (login_info) {
      // 更新现有登录信息
      if (!login_info->hasLoginID) {
        strncpy(login_info->loginID, selfLoginNum, sizeof(login_info->loginID) - 1);
        login_info->loginID[sizeof(login_info->loginID) - 1] = '\0';
        login_info->hasLoginID = 1;
      }
      login_info->lastActiveTime = now;

      // 释放预创建的登录信息
      if (new_login) {
        free(new_login);
        new_login = NULL;
      }
    } else if (new_login) {
      // 插入新登录信息
      g_hash_table_insert(g_tencent_meeting_login_hash, g_strdup(ip_key), new_login);
      new_login = NULL;  // 防止重复释放
    }
  }
  pthread_rwlock_unlock(&g_tencent_meeting_rwlock);

  // 清理资源
  if (new_login) {
    free(new_login);
  }
  g_free(ip_key);
}

// 更新或创建会议信息 - 个人会议账号和UID
static void update_user_meeting_num(
    struct flow_info *flow, uint64_t sessionId, const char *selfMeetingNum, const char *selfUID, uint8_t direction) {

  char *ip_key = generate_ip_key(flow, direction);
  if (!ip_key)
    return;

  // 生成person key (优先使用MSISDN，否则使用IP)
  char *person_key = generate_user_key(flow, direction);
  if (!person_key) {
    g_free(ip_key);
    return;
  }

  uint32_t now = g_config.g_now_time;

  // 1. 更新login表中的UID信息
  if (selfUID && selfUID[0] != '\0') {
    pthread_rwlock_wrlock(&g_tencent_meeting_rwlock);
    if (g_tencent_meeting_login_hash) {
      TencentMeetingLoginInfo *login_info = find_login_by_ip_unsafe(ip_key);
      if (login_info) {
        if (!login_info->hasUID) {
          strncpy(login_info->uid, selfUID, sizeof(login_info->uid) - 1);
          login_info->uid[sizeof(login_info->uid) - 1] = '\0';
          login_info->hasUID = 1;
        }
        login_info->lastActiveTime = now;
      } else {
        // 创建新的登录信息
        TencentMeetingLoginInfo *new_login = (TencentMeetingLoginInfo *)calloc(1, sizeof(TencentMeetingLoginInfo));
        if (new_login) {
          strncpy(new_login->uid, selfUID, sizeof(new_login->uid) - 1);
          new_login->uid[sizeof(new_login->uid) - 1] = '\0';
          new_login->hasUID = 1;
          new_login->hasLoginID = 0;
          new_login->lastActiveTime = now;
          g_hash_table_insert(g_tencent_meeting_login_hash, g_strdup(ip_key), new_login);
        }
      }
    }
    pthread_rwlock_unlock(&g_tencent_meeting_rwlock);
  }

  // 2. 更新person表中的会话信息
  TencentMeetingPersonInfo *new_person = NULL;
  TencentMeetingPersonInfo *person_info = find_person_by_key(person_key);

  if (!person_info) {
    // 在锁外预创建新会话信息
    new_person = create_new_person(flow, person_key, direction);
    if (!new_person) {
      g_free(ip_key);
      g_free(person_key);
      return;
    }

    // 设置会议信息
    new_person->meeting_info.sessionId = sessionId;
    if (selfMeetingNum && selfMeetingNum[0] != '\0') {
      strncpy(new_person->meeting_info.selfMeetingNum, selfMeetingNum, sizeof(new_person->meeting_info.selfMeetingNum) - 1);
      new_person->meeting_info.selfMeetingNum[sizeof(new_person->meeting_info.selfMeetingNum) - 1] = '\0';
    }
    if (selfUID && selfUID[0] != '\0') {
      strncpy(new_person->meeting_info.selfUID, selfUID, sizeof(new_person->meeting_info.selfUID) - 1);
      new_person->meeting_info.selfUID[sizeof(new_person->meeting_info.selfUID) - 1] = '\0';
    }
  }

  // 第二阶段：最小化写锁，只做插入或更新操作
  pthread_rwlock_wrlock(&g_tencent_meeting_rwlock);
  if (g_tencent_meeting_person_hash) {
    person_info = find_person_by_key_unsafe(person_key);

    if (person_info) {
      // 更新现有会话信息
      person_info->meeting_info.sessionId = sessionId;
      if (selfMeetingNum && selfMeetingNum[0] != '\0') {
        strncpy(person_info->meeting_info.selfMeetingNum, selfMeetingNum, sizeof(person_info->meeting_info.selfMeetingNum) - 1);
        person_info->meeting_info.selfMeetingNum[sizeof(person_info->meeting_info.selfMeetingNum) - 1] = '\0';
      }
      if (selfUID && selfUID[0] != '\0') {
        strncpy(person_info->meeting_info.selfUID, selfUID, sizeof(person_info->meeting_info.selfUID) - 1);
        person_info->meeting_info.selfUID[sizeof(person_info->meeting_info.selfUID) - 1] = '\0';
      }
      person_info->lastActiveTime = now;
      person_info->meeting_info.lastActiveTime = now;

      // 释放预创建的会话信息
      if (new_person) {
        free(new_person);
        new_person = NULL;
      }
    } else if (new_person) {
      // 插入新会话信息
      g_hash_table_insert(g_tencent_meeting_person_hash, g_strdup(person_key), new_person);
      new_person = NULL;  // 防止重复释放
    }
  }
  pthread_rwlock_unlock(&g_tencent_meeting_rwlock);

  // 清理资源
  if (new_person) {
    free(new_person);
  }
  g_free(ip_key);
  g_free(person_key);
}

// 删除超时用户信息 (线程不安全)
static int tencent_meeting_hash_delete_unthread_safe(void *hash, char *pStr) {
  if (NULL == hash || NULL == pStr) {
    return -1;
  }
  int ret = (int)g_hash_table_remove((GHashTable *)hash, (gconstpointer)pStr);
  return 0;
}

// 获取hash表大小
static size_t tencent_meeting_hash_get_size(void *hash) {
  if (NULL == hash) {
    return 0;
  }
  pthread_rwlock_rdlock(&g_tencent_meeting_rwlock);
  size_t ret = g_hash_table_size((GHashTable *)hash);
  pthread_rwlock_unlock(&g_tencent_meeting_rwlock);
  return ret;
}

// 聚合后的用户信息快照结构体，用于减少锁持有时间
typedef struct {
  char              key[64];       // 用户key
  ST_TencentMeeting meeting_info;  // 完整的腾讯会议信息
  uint8_t           isTimeout;     // 是否超时
} TencentMeetingAggregatedSnapshot;

// 腾讯会议用户管理线程
static void *tencent_meeting_user_thread(void *arg) {
#define MAX_TIME_OUT_NODE 1024

  while (1) {
    sleep(g_config.wx_session_timeloop);

    int hash_count = 0;
    int send = 0;
    int relation = 0;
    int timeout_count = 0;

    TencentMeetingAggregatedSnapshot snapshots[MAX_TIME_OUT_NODE];
    char                            *timeout_keys[MAX_TIME_OUT_NODE];
    uint32_t                         now = g_config.g_now_time;

    // 第一阶段：快速遍历person表，创建聚合快照，最小化锁持有时间
    pthread_rwlock_rdlock(&g_tencent_meeting_rwlock);
    if (g_tencent_meeting_person_hash) {
      GHashTableIter iter;
      g_hash_table_iter_init(&iter, g_tencent_meeting_person_hash);

      char                     *person_key = NULL;
      TencentMeetingPersonInfo *person_value = NULL;

      while (g_hash_table_iter_next(&iter, (gpointer *)&person_key, (gpointer *)&person_value) && hash_count < MAX_TIME_OUT_NODE) {
        if (NULL == person_value || NULL == person_key)
          continue;

        TencentMeetingAggregatedSnapshot *snapshot = &snapshots[hash_count];

        // 快速复制关键信息
        strncpy(snapshot->key, person_key, sizeof(snapshot->key) - 1);
        snapshot->key[sizeof(snapshot->key) - 1] = '\0';

        // 复制完整的会议信息
        memcpy(&snapshot->meeting_info, &person_value->meeting_info, sizeof(ST_TencentMeeting));

        // 判断是否超时
        snapshot->isTimeout = (now - person_value->lastActiveTime > g_config.wx_session_timeout) ? 1 : 0;
        if (snapshot->isTimeout && timeout_count < MAX_TIME_OUT_NODE) {
          timeout_keys[timeout_count++] = g_strdup(person_key);
        }

        // 尝试从login表中获取loginID信息进行聚合
        if (g_tencent_meeting_login_hash) {
          // 从person_key中提取IP（如果person_key是IP的话）
          // 或者根据trailer信息查找对应的IP
          char ip_key[32] = {0};

          // 如果person_key就是IP，直接使用
          if (strchr(person_key, '.') || strchr(person_key, ':')) {
            strncpy(ip_key, person_key, sizeof(ip_key) - 1);
          } else {
            // 如果person_key是MSISDN，需要通过其他方式找到对应的IP
            // 这里简化处理，可以根据实际需求优化
            // 暂时跳过聚合
            ip_key[0] = '\0';
          }

          if (ip_key[0] != '\0') {
            TencentMeetingLoginInfo *login_info = (TencentMeetingLoginInfo *)g_hash_table_lookup(g_tencent_meeting_login_hash, (gconstpointer)ip_key);
            if (login_info) {
              // 聚合loginID信息
              if (login_info->hasLoginID) {
                strncpy(snapshot->meeting_info.selfLoginNum, login_info->loginID, sizeof(snapshot->meeting_info.selfLoginNum) - 1);
                snapshot->meeting_info.selfLoginNum[sizeof(snapshot->meeting_info.selfLoginNum) - 1] = '\0';
              }
            }
          }
        }

        hash_count++;
      }
    }
    pthread_rwlock_unlock(&g_tencent_meeting_rwlock);

    // 第二阶段：处理快照数据，发送聚合信息，统计数据
    for (int i = 0; i < hash_count; i++) {
      TencentMeetingAggregatedSnapshot *snapshot = &snapshots[i];

      // 统计建联人数 (有MSISDN的用户)
      if (snapshot->meeting_info.trailer.MSISDN != 0) {
        relation++;
      }

      // 统计发送人数 (有会话ID的用户)
      if (snapshot->meeting_info.sessionId) {
        send++;
      }

      // 发送用户信息到聚合服务器
      if (g_tencent_meeting_handle && snapshot->meeting_info.sessionId) {
        // 直接发送完整的会议信息
        ST_TencentMeeting person_info = snapshot->meeting_info;
        person_info.isTimeout = snapshot->isTimeout;
        dpi_TrailerUpdateTS(&person_info.trailer);

        // 发送数据
        wxc_sendMsg(
            g_tencent_meeting_handle, (const unsigned char *)&person_info, sizeof(ST_TencentMeeting), WXCS_TENCENT_MEETING);
      }
    }

    // 打印统计信息
    char   buffer[26];
    time_t timer;
    time(&timer);
    struct tm *tm_info = localtime(&timer);
    strftime(buffer, 26, "%Y-%m-%d %H:%M:%S", tm_info);
    printf("TECENT_MEETING :[%s] 已发送/已超时/已建联/总人数=[%d/%d/%d/%d]\n", buffer, send, timeout_count, relation, hash_count);

    // 第三阶段：删除超时用户，最小化写锁持有时间
    if (timeout_count > 0) {
      pthread_rwlock_wrlock(&g_tencent_meeting_rwlock);

      // 删除超时的person信息
      if (g_tencent_meeting_person_hash) {
        for (int i = 0; i < timeout_count; i++) {
          tencent_meeting_hash_delete_unthread_safe(g_tencent_meeting_person_hash, timeout_keys[i]);
        }
      }

      // 同时清理超时的login信息
      if (g_tencent_meeting_login_hash) {
        GHashTableIter iter;
        g_hash_table_iter_init(&iter, g_tencent_meeting_login_hash);

        char                     *login_key = NULL;
        TencentMeetingLoginInfo  *login_value = NULL;
        GList                    *keys_to_remove = NULL;

        while (g_hash_table_iter_next(&iter, (gpointer *)&login_key, (gpointer *)&login_value)) {
          if (login_value && (now - login_value->lastActiveTime > g_config.wx_session_timeout)) {
            keys_to_remove = g_list_prepend(keys_to_remove, g_strdup(login_key));
          }
        }

        // 删除超时的login信息
        for (GList *l = keys_to_remove; l != NULL; l = l->next) {
          g_hash_table_remove(g_tencent_meeting_login_hash, l->data);
          g_free(l->data);
        }
        g_list_free(keys_to_remove);
      }

      // 释放timeout_keys
      for (int i = 0; i < timeout_count; i++) {
        g_free(timeout_keys[i]);
      }

      pthread_rwlock_unlock(&g_tencent_meeting_rwlock);
    }
  }

  return NULL;
}


// 解析个人会议数据 (0x36开头)
static int dissect_tencent_meeting_36(
    struct flow_info *flow, int direction, uint32_t seq, const uint8_t *payload, const uint32_t payload_len) {
  if (payload_len < 20) {
    return 0;
  }

  Tencent_meeting_session *session = (Tencent_meeting_session *)flow->app_session;
  if (!session) {
    return 0;
  }

  // 解析包长度
  // uint16_t packetLen = get_uint16_ntohs(payload, 1);
  // if (payload_len < packetLen) {
  //   return 0;
  // }

  // 检查固定字段
  if (get_uint32_ntohl(payload, 3) != 0x00000000) {
    return 0;
  }

  // 检查0x03a102
  if (get_uint16_ntohs(payload, 7) != 0x03a1 || payload[9] != 0x02) {
    return 0;
  }
  // 02 + 7字节不明
  snprintf(session->selfUID, sizeof(session->selfUID), "%lu", pntoh64(payload + 9));
  // 解析sessionID (4字节)
  session->sessionId = get_uint32_ntohl(payload, 17);
  // 更新个人会议账号信息到全局hash表
  update_user_meeting_num(flow, session->sessionId, session->selfMeetingNum, session->selfUID, direction);
  return 0;
}
// 解析个人会议数据 (0x51开头)
static int dissect_tencent_meeting_51(
    struct flow_info *flow, int direction, uint32_t seq, const uint8_t *payload, const uint32_t payload_len) {
  if (payload_len < 16) {
    return 0;
  }

  Tencent_meeting_session *session = (Tencent_meeting_session *)flow->app_session;
  if (!session) {
    return 0;
  }

  // 检查固定字段 0x0011
  if (get_uint16_ntohs(payload, 1) != 0x0011) {
    return 0;
  }

  // 检查0x02
  if (payload[5] != 0x02) {
    return 0;
  }
  snprintf(session->selfUID, sizeof(session->selfUID), "%lu", pntoh64(payload + 5));
  // 解析sessionID (4字节)
  session->sessionId = get_uint32_ntohl(payload, 13);
  // 更新个人会议账号信息到全局hash表
  update_user_meeting_num(flow, session->sessionId, session->selfMeetingNum, session->selfUID, direction);
  return 0;
}
// 解析个人常规账号 (TCP)
static int dissect_tencent_meeting_tcp(
    struct flow_info *flow, int direction, uint32_t seq, const uint8_t *payload, const uint32_t payload_len) {
  if (payload_len < 32) {
    return 0;
  }

  // 检查标识符 0x00001770
  if (get_uint32_ntohl(payload, 4) != 0x00001770) {
    return 0;
  }

  Tencent_meeting_session *session = (Tencent_meeting_session *)flow->app_session;
  if (!session) {
    return 0;
  }

  // 解析个人常规账号
  char     selfLoginNum[19] = {0};
  uint32_t accountOffset = 0;

  // 根据方向确定账号位置
  if (direction == 0) {
    uint64_t info_len = 0;
    info_len = get_uint32_ntohl(payload, 9);
    if (info_len >= payload_len - 9) {
      return 0;
    }
    if (get_uint32_ntohl(payload, info_len + 9) == 0x14000000 && get_uint16_ntohs(payload, info_len + 9 + 3) == 0x0016) {  // 上行
      // 查找0x1400000016标识
      accountOffset = info_len + 14;
    }
  } else {  // 下行
            // 查找0x1400000016标识
    if (get_uint32_ntohl(payload, 9) == 0x14000000 && get_uint16_ntohs(payload, 9 + 3) == 0x0016) {
      accountOffset = 14;
    }
  }

  if (accountOffset > 0 && accountOffset + 18 <= payload_len) {
    memcpy(selfLoginNum, payload + accountOffset, 18);
    selfLoginNum[18] = '\0';
    strncpy(session->selfLoginNum, selfLoginNum, sizeof(session->selfLoginNum) - 1);
    session->selfLoginNum[sizeof(session->selfLoginNum) - 1] = '\0';

    // 更新个人常规账号信息到全局hash表
    update_user_login_num(flow, selfLoginNum, direction);
  }

  return 0;
}
static int dissect_tencent_meeting_43(
    struct flow_info *flow, int direction, uint32_t seq, const uint8_t *payload, const uint32_t payload_len) {
  if (payload_len < 16) {
    return 0;
  }
  Tencent_meeting_session *session = (Tencent_meeting_session *)flow->app_session;
  if (!session) {
    return 0;
  }
  uint16_t len = get_uint16_ntohs(payload, 1);
  uint8_t  tag = payload[3];
  if (tag == 0x51) {
    int result = dissect_tencent_meeting_51(flow, direction, seq, payload + 3, payload_len - 3);
  }
  return 0;
}

static int dissect_tencent_meeting_chat(
    struct flow_info *flow, int direction, uint32_t seq, const uint8_t *payload, const uint32_t payload_len, uint8_t flag) {
  if (g_config.protocol_switch[PROTOCOL_TENCENT_MEETING] == 0) {
    return 0;
  }

  if (NULL == flow->app_session) {
    Tencent_meeting_session *tcmeeting_session;
    tcmeeting_session = malloc(sizeof(Tencent_meeting_session));
    if (NULL == tcmeeting_session) {
      DPI_LOG(DPI_LOG_ERROR, "error on malloc Tencent_meeting_session");
      return 0;
    }
    memset(tcmeeting_session, 0, sizeof(Tencent_meeting_session));
    flow->app_session = tcmeeting_session;
  }

  Tencent_meeting_session *session = (Tencent_meeting_session *)flow->app_session;

  uint16_t dstPort = ntohs(flow->tuple.inner.port_dst);
  if (dstPort == 443 || dstPort == 8080 || dstPort == 80) {
    direction = 0;  //c2s
  } else {
    direction = 1;  //s2c
  }

  // 更新包计数
  if (direction == 0) {  // C2S
    session->c2sPackCount++;
    session->c2sByteCount += payload_len;
  } else {  // S2C
    session->s2cPackCount++;
    session->s2cByteCount += payload_len;
  }

  int result = 0;
  switch (payload[0]) {
    case 0x36:
      // 解析sessionID
      result = dissect_tencent_meeting_36(flow, direction, seq, payload, payload_len);
      break;
    case 0x51:
      // 解析sessionID
      result = dissect_tencent_meeting_51(flow, direction, seq, payload, payload_len);
      break;
    case 0x43:
      // 解析sessionID
      result = dissect_tencent_meeting_43(flow, direction, seq, payload, payload_len);
      break;

    case 0x41:
      // 更新时间
      break;
    default:
      if (get_uint32_ntohl(payload, 4) != 0x00001770)
        return 0;
      // 解析个人常规账号
      result = dissect_tencent_meeting_tcp(flow, direction, seq, payload, payload_len);
      break;
  }

  return result;
}

static void identify_tencent_meeting_chat(struct flow_info *flow, const uint8_t *payload, const uint16_t payload_len) {
  if (g_config.protocol_switch[PROTOCOL_TENCENT_MEETING] == 0) {
    return;
  }

  if (payload_len < 20) {
    return;
  }
  /* 判断报文的目标端口  */
  int port_src = ntohs(flow->tuple.inner.port_src);
  int port_dst = ntohs(flow->tuple.inner.port_dst);
  if (flow->tuple.inner.proto == 6) {
    if ((80 == port_dst || 80 == port_src || 443 == port_dst || 443 == port_src || 8080 == port_dst || 8080 == port_src) &&
        (get_uint32_ntohl(payload, 4) == 0x00001770)) {
      // tcp个人常规
      flow->real_protocol_id = PROTOCOL_TENCENT_MEETING;
    } else if ((443 == port_dst || 443 == port_src) &&
               (payload[0] == 0x36 || payload[0] == 0x41 || payload[0] == 0x51)) {
      // tcp个人会议
      flow->real_protocol_id = PROTOCOL_TENCENT_MEETING;
    }
  } else {
    if ((443 == port_dst || 443 == port_src) &&
        (payload[0] == 0x36 || payload[0] == 0x41 || payload[0] == 0x51)) {
      flow->real_protocol_id = PROTOCOL_TENCENT_MEETING;
    }
    return;
  }
}

// 初始化腾讯会议用户管理线程
int init_tencent_meeting_user_thread(void) {
  // 如果腾讯会议功能没有开启
  if (g_config.protocol_switch[PROTOCOL_TENCENT_MEETING] == 0) {
    return 0;
  }

  int status = pthread_create(&g_tencent_meeting_thread, NULL, tencent_meeting_user_thread, NULL);
  if (status != 0) {
    DPI_LOG(DPI_LOG_ERROR, "error on create tencent_meeting_user thread");
    exit(-1);
  }

  return 0;
}

// 等待腾讯会议用户管理线程结束
int wait_tencent_meeting_user_thread_finish(void) { return pthread_join(g_tencent_meeting_thread, NULL); }

void init_tencent_meeting_chat_dissector(void) {
  // 初始化wxc连接
  if (NULL == g_tencent_meeting_handle) {
    wxc_init(&g_tencent_meeting_handle, g_config.wx_voice_ip, g_config.wx_voice_port);
  }

  // 初始化hash表
  init_tencent_meeting_hash();

  // 启动用户管理线程

  port_add_proto_head(IPPROTO_UDP, 443, PROTOCOL_TENCENT_MEETING);

  udp_detection_array[PROTOCOL_TENCENT_MEETING].proto = PROTOCOL_TENCENT_MEETING;
  udp_detection_array[PROTOCOL_TENCENT_MEETING].identify_func = identify_tencent_meeting_chat;
  udp_detection_array[PROTOCOL_TENCENT_MEETING].dissect_func = dissect_tencent_meeting_chat;
  port_add_proto_head(IPPROTO_TCP, 80, PROTOCOL_TENCENT_MEETING);
  port_add_proto_head(IPPROTO_TCP, 8080, PROTOCOL_TENCENT_MEETING);
  port_add_proto_head(IPPROTO_TCP, 443, PROTOCOL_TENCENT_MEETING);

  tcp_detection_array[PROTOCOL_TENCENT_MEETING].proto = PROTOCOL_TENCENT_MEETING;
  tcp_detection_array[PROTOCOL_TENCENT_MEETING].identify_func = identify_tencent_meeting_chat;
  tcp_detection_array[PROTOCOL_TENCENT_MEETING].dissect_func = dissect_tencent_meeting_chat;

  DPI_SAVE_AS_BITMASK(udp_detection_array[PROTOCOL_TENCENT_MEETING].excluded_protocol_bitmask, PROTOCOL_TENCENT_MEETING);
  DPI_SAVE_AS_BITMASK(tcp_detection_array[PROTOCOL_TENCENT_MEETING].excluded_protocol_bitmask, PROTOCOL_TENCENT_MEETING);

  return;
}
