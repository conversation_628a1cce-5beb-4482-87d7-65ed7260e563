/****************************************************************************************
 * 文 件 名 : wxcs_person.cpp
 * 项目名称 : YVBD1207001B
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 设    计 : root      '2019-01-14
* 编    码 : root      '2019-01-14
* 修    改 :
****************************************************************************************
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
***************************************************************************************/
#include <arpa/inet.h>
#include "wxcs_person.h"
#include "jhash.h"
#include "wxcs_utils.h"
#include "wxcs_logger.h"
#include "wxcs_config.h"
#include "wxcs_session.h"

#include <iostream>
#include <sstream>
#include <algorithm>
#include <iomanip>
#include <cstdlib>
#include <muduo/net/Endian.h>



using namespace muduo;
using namespace muduo::net;

static std::map<int, std::string> g_jc_rat = {
    {0x01,            "UTRAN"},
    {0x02,            "GERAN"},
    {0x03,            "WLAN"},
    {0x04,            "GAN"},
    {0x05,            "HSPA Evolution"},
    {0x06,            "EUTRAN"},
    {0x07,            "Virtual"},
    {0x08,            "EUTRAN-NB-IoT"},
    {0x09,            "LTE-M"},
    {0x0a,            "NR"}
};

WxcsPerson::WxcsPerson() {}
WxcsPerson::WxcsPerson(
        uint8_t *SessionID,   int SessionIDLen, uint8_t SessionType,
        uint16_t client_port, uint16_t server_port,
        uint32_t client_ip,   uint32_t server_ip,
        uint32_t PersonLastActiveTime,
        ST_trailer *trailer)
{
    this->LastActiveTime   = PersonLastActiveTime;
    this->bHasLteInfo      = (TRAILER_NO == trailer->trailerType) ? 0 : 1;
    this->trailerType      = trailer->trailerType;
    this->SessionType      = SessionType;
    this->SessionID        = bytes_to_hexstring(SessionID, SessionIDLen);

    this->TEID             = trailer->TEID;
    this->MSISDN           = trailer->MSISDN;
    this->IMSI             = trailer->IMSI;
    this->IMEI             = trailer->IMEI;
    this->TAC              = trailer->TAC;
    this->DevName          = trailer->DevName;
    this->Operator         = trailer->Operator;
    this->Area             = trailer->Area;

    this->HW_BFLAG         = trailer->BFLAG;
    this->HW_APN           = trailer->APN;
    this->HW_NCODE         = trailer->NCODE;
    this->HW_ECGI          = trailer->ECGI;
    this->HW_LAC           = trailer->LAC;
    this->HW_SAC           = trailer->SAC;
    this->HW_CI            = trailer->CI;

    this->RT_PLMN_ID       = trailer->PLMN_ID;
    this->RT_ULI           = trailer->ULI;
    this->RT_BS            = trailer->BS;  // 适配金陵新旧标签
    this->RT_DomainName    = trailer->DomainName;
    this->DPI_Node_ID      = trailer->DPI_Node_id;
    this->jc_rat           = trailer->rat;
    this->nci              = trailer->nci;

    this->ip_version       = 4;                  // 基类 接口不便于改动, 默认使用 ipv4
    this->client_ip        = client_ip;          // 基类 接口不便于改动, 默认使用 ipv4
    this->server_ip        = server_ip;          // 基类 接口不便于改动, 默认使用 ipv4
    this->client_port      = client_port;
    this->server_port      = server_port;

    fixed_account_         = trailer->fixed_account;
}

uint64_t WxcsPerson::getMsisdn() const
{
    return MSISDN;
}

uint64_t WxcsPerson::getImsi() const
{
    return IMSI;
}
uint64_t WxcsPerson::getImei() const
{
    return IMEI;
}
uint64_t WxcsPerson::getSrcIP() const
{
    return client_ip;
}
uint64_t WxcsPerson::getSrcPort() const
{
    return client_port;
}
uint32_t WxcsPerson::getDPI_NodeID() const
{
    return DPI_Node_ID;
}

uint64_t WxcsPerson::getPersonID() const
{
    if (CFG->GetValueOf<bool>("RTL_TAGGED_MODE", false) || bHasLteInfo)
    {
        if (MSISDN)
        {
            return MSISDN;
        }
        else
        if (IMSI)
        {
            return IMSI;
        }
        else
        if (IMEI)
        {
            return IMEI;
        }
    }

    return client_ip;
}

uint64_t WxcsPerson::getPersonUUID() const
{
    return getPersonID() + jhash(DevName.c_str(), DevName.length(), 31) + jhash(Operator.c_str(), Operator.length(), 31);
}

std::string WxcsPerson::getDevName() const
{
    return DevName;
}

std::string WxcsPerson::getOperator() const
{
    return Operator;
}

bool  WxcsPerson::wasUncrediableId() const
{
    // 指明为 tagged 模式
    if (CFG->GetValueOf<bool>("RTL_TAGGED_MODE", false) || bHasLteInfo)
    {
        // 任意一个为0 即认为不可信
        return (MSISDN == 0 && IMSI == 0 && IMEI == 0);
    }

    // 固网，认为可信，使用 clientIp
    return false;
}

std::string WxcsPerson::getPrintablePersonID() const
{
    if (CFG->GetValueOf<bool>("RTL_TAGGED_MODE", false) || bHasLteInfo)
    {
        if (MSISDN)
        {
            return std::to_string(MSISDN);
        }
        else
        if (IMSI)
        {
            return "IMSI:" + std::to_string(IMSI);
        }
        else
        if (IMEI)
        {
            return "IMEI:" + std::to_string(IMEI);
        }
    }

    if(6 == ip_version)
    {
        char buff[64];
        return inet_ntop(AF_INET6, client_ipv6, buff, sizeof(buff));
    }

    if(4 == ip_version && client_ip)
    {
        char buff[64];
        return inet_ntop(AF_INET, &client_ip, buff, sizeof(buff));
    }

    return "";
}

std::string WxcsPerson::getPrintablePersonMobileID() const
{
    if (bHasLteInfo)
    {
        return std::to_string(MSISDN) + "," + std::to_string(IMEI) + "," + std::to_string(IMSI);
    }

    char burrer_ip[64];

    if (4 == ip_version) {
        return inet_ntop(AF_INET, &client_ip, burrer_ip, 64);
    }

    if (6 == ip_version) {
        return inet_ntop(AF_INET6, client_ipv6, burrer_ip, 64);
    }
    return "";
}

std::string WxcsPerson::getPrintableSessionID() const
{
    return  SessionID;
}

std::string WxcsPerson::getClientIP() const
{
    char burrer_ip[64];

    if(4 == ip_version)
    {
        return inet_ntop(AF_INET, &client_ip, burrer_ip, 64);
    }

    if(6 == ip_version)
    {
        return inet_ntop(AF_INET6, client_ipv6, burrer_ip, 64);
    }
    return "";
}

std::string WxcsPerson::getClientPubIP() const
{
    char burrer_ip[64];

    if(4 == ip_version && cli_pub_ip)
    {
        return inet_ntop(AF_INET, &cli_pub_ip, burrer_ip, 64);
    }
    return "";
}


std::string WxcsPerson::getServerIP() const
{
    char burrer_ip[64];

    if(4 == ip_version)
    {
        return inet_ntop(AF_INET, &server_ip, burrer_ip, 64);
    }

    if(6 == ip_version)
    {
        return inet_ntop(AF_INET6, server_ipv6, burrer_ip, 64);
    }
    return "";
}

std::string WxcsPerson::getPrintableULI() const
{
    uint32_t ULI_le = sockets::networkToHost32(RT_ULI);
    uint32_t eNodeBid = 0, CellId = 0;
    uint32_t LAC = 0, SAC = 0;

    if (trailerType == TRAILER_ZXSK) {
        if (nci != 0) {
            return "5G:0x" + [](uint64_t nci) {
                std::stringstream ss;
                ss << std::hex << std::uppercase << std::setfill('0') << std::setw(9) << nci;
                return ss.str();
            }(nci);
        } else {
            eNodeBid = ULI_le >> 12;
            CellId = (ULI_le >> 4) & 0xff;
            return "4G:" + bytes_to_hexstring(RT_ULI) +
                ",eNodeBid:" + std::to_string(eNodeBid) +
                ",CellId:" + std::to_string(CellId);
        }
    }

    if(0 == RT_ULI)
    {
        return "";
    }

    if (jc_rat > 0 && jc_rat <= 0x0a) {
        eNodeBid = ULI_le >> 12;
        CellId = (ULI_le >> 4) & 0xff;
        return g_jc_rat[jc_rat] + ":" + bytes_to_hexstring(RT_ULI) +
                ",eNodeBid:" + std::to_string(eNodeBid) +
                ",CellId:" + std::to_string(CellId);
    } else {
        switch (RT_BS & 0x0F)
        {
        case 0xd:
            // ULI:0xabcdefg0: 0xabcde eNodeBid, 0xfg CellId
            // 4G: hex,eNodeBid:xxxx,CellId:xxx
            eNodeBid = ULI_le >> 12;
            CellId = (ULI_le >> 4) & 0xff;

            return "4G:" + bytes_to_hexstring(RT_ULI) +
                ",eNodeBid:" + std::to_string(eNodeBid) +
                ",CellId:" + std::to_string(CellId);

        case 0xc:
            // ULI:0xabcdefgh: 0xabcd LAC, 0xefgh SAC
            // 3G: hex,LAC:xxxx,SAC:xxxx
            LAC = ULI_le >> 16;
            SAC = ULI_le & 0xffff;

            return "3G:" + bytes_to_hexstring(RT_ULI) +
                ",LAC:" + std::to_string(LAC) +
                ",SAC:" + std::to_string(SAC);

        default:
            return "UN:" + bytes_to_hexstring(RT_ULI);
        }
    }


}

uint32_t WxcsPerson::getLastActiveTime() const
{
    return LastActiveTime;
}

std::string WxcsPerson::toStrRecord(char sep _U_) const
{
    return "";
}

std::string WxcsPerson::getStrTrailer(char sep) const
{
    std::string strTrailer;

    CONS_RECORD_FIELD_NUM  (strTrailer, trailerType,          sep);
    CONS_RECORD_FIELD_BYTES(strTrailer, TEID,                 sep);
    if (MSISDN != 0) {
        CONS_RECORD_FIELD_NUM  (strTrailer, MSISDN, sep);
    } else {
        CONS_RECORD_FIELD_TEXT (strTrailer, fixed_account_, sep);
    }
    CONS_RECORD_FIELD_NUM  (strTrailer, IMSI,                 sep);
    CONS_RECORD_FIELD_NUM  (strTrailer, IMEI,                 sep);
    if (trailerType == TRAILER_ZXSK && nci != 0) {
        CONS_RECORD_FIELD_BYTES(strTrailer, htonl(TAC) ,   sep);
    } else {
        CONS_RECORD_FIELD_BYTES(strTrailer, (uint16_t)TAC,   sep);
    }
    CONS_RECORD_FIELD_TEXT (strTrailer, Operator,             sep);
    CONS_RECORD_FIELD_TEXT (strTrailer, DevName,              sep);
    CONS_RECORD_FIELD_TEXT (strTrailer, Area,                 sep);

    CONS_RECORD_FIELD_BYTES(strTrailer, HW_BFLAG,             sep);
    CONS_RECORD_FIELD_TEXT (strTrailer, HW_APN,               sep);
    CONS_RECORD_FIELD_BYTES(strTrailer, HW_NCODE,             sep);
    CONS_RECORD_FIELD_BYTES(strTrailer, HW_ECGI,              sep);
    CONS_RECORD_FIELD_BYTES(strTrailer, HW_LAC,               sep);
    CONS_RECORD_FIELD_BYTES(strTrailer, HW_SAC,               sep);
    CONS_RECORD_FIELD_BYTES(strTrailer, HW_CI,                sep);

    CONS_RECORD_FIELD_BYTES(strTrailer, RT_PLMN_ID,           sep);
    CONS_RECORD_FIELD      (strTrailer, getPrintableULI(),    sep);
    if (jc_rat != 0) {
        CONS_RECORD_FIELD_BYTES(strTrailer, jc_rat,           sep);
    } else {
        CONS_RECORD_FIELD_BYTES(strTrailer, RT_BS,                sep);
    }
    CONS_RECORD_FIELD_TEXT (strTrailer, RT_DomainName,        sep);

    CONS_RECORD_FIELD      (strTrailer, getClientIP(),        sep);
    CONS_RECORD_FIELD      (strTrailer, getServerIP(),        sep);
    CONS_RECORD_FIELD_NUM  (strTrailer, client_port,          sep);
    CONS_RECORD_FIELD_NUM  (strTrailer, server_port,          sep);

    return strTrailer;
}

std::string WxcsPerson::toStrBlankRecord(char sep _U_)
{
    return "";
}


//////////////////// ZOOM Person //////////////////////////////////////////
WxcsZoomPerson::WxcsZoomPerson(ST_ZOOM_person *p)
    : WxcsPerson(p->SID, sizeof(p->SID), 0,
                 p->client_port, p->server_port,
                 p->client_ip.ipv4, p->server_ip.ipv4,
                 p->last,
                 &p->trailer)
{
    C2S_A_Packet = p->C2S_A_Packet;
    S2C_A_Packet = p->S2C_A_Packet;

    C2S_V_Packet = p->C2S_V_Packet;
    S2C_V_Packet = p->S2C_V_Packet;

    C2S_C_Packet = p->C2S_C_Packet;
    S2C_C_Packet = p->S2C_C_Packet;

    C2S_K_Packet = p->C2S_K_Packet;
    S2C_K_Packet = p->S2C_K_Packet;

    first        = p->first;
    ans          = p->answered;
    last         = p->last;
    isTimeout    = p->isTimeout;
}

std::string WxcsZoomPerson::toStrRecord(char sep) const
{
    std::string strRecord;

    strRecord += getStrTrailer(sep);  // 建联信息

    char buff[64];
    snprintf(buff, sizeof(buff), "CA=%u&SA=%u&CV=%u&SV=%u&CC=%u&SC=%u&CK=%u&SK=%u",
            C2S_A_Packet, S2C_A_Packet,
            C2S_V_Packet, S2C_V_Packet,
            C2S_C_Packet, S2C_C_Packet,
            C2S_K_Packet, S2C_K_Packet);
    std::string res = buff;

    int  C2S_P  = C2S_A_Packet + C2S_V_Packet + C2S_C_Packet + C2S_K_Packet;
    int  S2C_P  = S2C_A_Packet + S2C_V_Packet + S2C_C_Packet + S2C_K_Packet;
    int  is_ans = 0;
    int  wait   = 0;
    int  talk   = 0;
    if(0 != ans)
    {
        is_ans = 1;
        wait = ans  - first;
        talk = last - ans;
    }
    else
    {
        is_ans = 0;
        wait   = last  - first;
        talk   = 0;
    }


    // statistics
    CONS_RECORD_FIELD_NUM(strRecord,   C2S_P,        sep);
    CONS_RECORD_FIELD_NUM(strRecord,   S2C_P,        sep);

    // date
    CONS_RECORD_FIELD_TIME(strRecord,  first,        sep);
    CONS_RECORD_FIELD_TIME(strRecord,  last,         sep);

    CONS_RECORD_FIELD_NUM(strRecord,   is_ans,       sep);
    CONS_RECORD_FIELD_NUM(strRecord,   wait,         sep);
    CONS_RECORD_FIELD_NUM(strRecord,   talk,         sep);

    //保留字段
    CONS_RECORD_FIELD_TEXT(strRecord,  res, sep);

    return strRecord;
}

std::string WxcsZoomPerson::toStrBlankRecord(char sep)
{
    std::string strRecord;
    for (int i = 0; i < 32; i++)
    {
        strRecord += std::string("\"\"") + sep;
    }
    return strRecord;
}


//////////////////// WxcsGroupHeadPerson //////////////////////////////////////////
WxcsGroupHeadPerson::WxcsGroupHeadPerson()
{
}


WxcsGroupHeadPerson::WxcsGroupHeadPerson(ST_wxGroupHead* pGroupHeadUpdate)
    :WxcsPerson(
            (uint8_t*)pGroupHeadUpdate->PersonURL,
            strlen((const char*)pGroupHeadUpdate->PersonURL),                 /* URL  作为HASH_Key */
            1,                                                                /* 群行为, 填1 */
            pGroupHeadUpdate->PersonSrcPort, pGroupHeadUpdate->PersonDstPort, /* IP   */
            pGroupHeadUpdate->PersonSrcIp, pGroupHeadUpdate->PersonDstIp,     /* Port */
            pGroupHeadUpdate->PersonLastActiveTime,                           /* Active Time */
            &pGroupHeadUpdate->trailer)     /* 建联标签 */
    , PersonHost((const char*)pGroupHeadUpdate->PersonHost)
    , PersonUrl((const char*)pGroupHeadUpdate->PersonURL)
    , PersonUin((const char*)pGroupHeadUpdate->PersonUin)
    , PersonRef((const char*)pGroupHeadUpdate->PersonRef)
    , PersonAgent((const char*)pGroupHeadUpdate->PersonAgent)
    , PersonAcceptEncoding((const char*)pGroupHeadUpdate->PersonAcceptEncoding)
    , PersonAcceptLanguage((const char*)pGroupHeadUpdate->PersonAcceptLanguage)
{
    if(6 == pGroupHeadUpdate->ip_version)
    {
        ip_version = 6;
        memcpy(client_ipv6, pGroupHeadUpdate->client_ip.ipv6, 16);
        memcpy(server_ipv6, pGroupHeadUpdate->server_ip.ipv6, 16);
    }
}


int WxcsGroupHeadPerson::dumpGroupHeadPerson() const
{
    return 0;
}

// 写一个人的信息
std::string WxcsGroupHeadPerson::toStrRecord(char sep) const
{
    std::string strRecord;
    strRecord += getStrTrailer(sep);  // 建联信息

    // HTTP
    CONS_RECORD_FIELD(strRecord,       PersonHost, sep);
    CONS_RECORD_FIELD(strRecord,       PersonUin, sep);
    CONS_RECORD_FIELD(strRecord,       PersonRef, sep);
    CONS_RECORD_FIELD(strRecord,       PersonAgent, sep);
    CONS_RECORD_FIELD(strRecord,       PersonAcceptEncoding, sep);
    CONS_RECORD_FIELD(strRecord,       PersonAcceptLanguage, sep);

    // date
    CONS_RECORD_FIELD_TIME(strRecord,  LastActiveTime, sep);

    CONS_RECORD_FIELD_TEXT(strRecord,  "", sep);
    return strRecord;
}

//人数不够, 写空, 注意每个人的字段数
std::string WxcsGroupHeadPerson::toStrBlankRecord(char sep)
{
    std::string strRecord;
    for (int i = 0; i < 32; i++)
    {
        strRecord += std::string("\"\"") + sep;
    }

    return strRecord;
}


//////////////////// WxcsPositionPerson //////////////////////////////////////////
WxcsPositionPerson::WxcsPositionPerson()
{
}

WxcsPositionPerson::WxcsPositionPerson(ST_wxPosition * pPositionUpdate)
    :WxcsPerson(
        (uint8_t*)pPositionUpdate->PersonURL,
        strlen((const char*)pPositionUpdate->PersonURL),                   /* URL  作为HASH_Key */
        1,                                                                 /* 群体行为, 填1 */
        pPositionUpdate->PersonSrcPort, pPositionUpdate->PersonDstPort,    /* IP   */
        pPositionUpdate->PersonSrcIp, pPositionUpdate->PersonDstIp,        /* Port */
        pPositionUpdate->PersonLastActiveTime,
        &pPositionUpdate->trailer)
    , PersonHost((const char*)pPositionUpdate->PersonHost)
    , PersonUrl((const char*)pPositionUpdate->PersonURL)
    , PersonAgent((const char*)pPositionUpdate->PersonAgent)
    , PersonAcceptEncoding((const char*)pPositionUpdate->PersonAcceptEncoding)

{
    std::string &&position = PersonUrl.substr(PersonUrl.find("center="), PersonUrl.find("&zoom") - PersonUrl.find("center="));

    longitude  = position.substr(position.find_first_of('=') + 1, position.find_first_of(',') - position.find_first_of('=') - 1);

    latitude   = position.substr(position.find_first_of(',') + 1, position.find_first_of('&') - position.find_first_of(','));

    zoom       = PersonUrl.substr(PersonUrl.find_last_of('o') + 3, 2);
}

int WxcsPositionPerson::dumpPositionPerson() const
{
    return 0;
}

std::string WxcsPositionPerson::toStrRecord(char sep) const
{
    std::string strRecord;
    strRecord += getStrTrailer(sep);  // 建联信息

    // HTTP
    CONS_RECORD_FIELD(strRecord,       PersonHost, sep);
    CONS_RECORD_FIELD(strRecord,       PersonAgent, sep);
    CONS_RECORD_FIELD(strRecord,       PersonAcceptEncoding, sep);
    CONS_RECORD_FIELD(strRecord,       PersonUrl, sep);

    // date
    CONS_RECORD_FIELD_TIME(strRecord,  LastActiveTime, sep);
    CONS_RECORD_FIELD_TEXT(strRecord,  "",       sep);
    return strRecord;
}

std::string WxcsPositionPerson::toStrBlankRecord(char sep)
{
    std::string strRecord;
    // 对应上面字段个数
    for (int i = 0; i < 30; i++)
    {
        strRecord.append(std::string("\"\"") + sep);
    }

    return strRecord;
}

std::string WxcsPositionPerson::getLongitude() const
{
    return longitude;
}

std::string WxcsPositionPerson::getLatitude() const
{
    return latitude;
}

std::string WxcsPositionPerson::getUrl() const
{
    return PersonUrl;
}

std::string WxcsPositionPerson::getZoom() const
{
    return zoom;
}

//////////////////// WxcsQQGroupPerson //////////////////////////////////////////
WxcsQQGroupPerson::WxcsQQGroupPerson()
{
}

WxcsQQGroupPerson::WxcsQQGroupPerson(ST_qqGroup *msg)
    : WxcsPerson((uint8_t*)&msg->groupId, sizeof msg->groupId
                 , WXCS_SESSION_NONE
                 , msg->srcPort, msg->dstPort
                 , msg->srcIp, msg->dstIp
                 , msg->lastActiveTime
                 , &msg->trailer)
    , selfQQ_(msg->selfQQNum)
    , startTime_(msg->firstActiveTime)
    , c2sPackCount_(msg->c2sPackCount)
    , c2sByteCount_(msg->c2sByteCount)
    , s2cPackCount_(msg->s2cPackCount)
    , s2cByteCount_(msg->s2cByteCount)
    , isCurrent_(msg->isTimeout)
{
}

std::string WxcsQQGroupPerson::toStrRecord(char sep) const
{
    std::string strRecord;
    strRecord += getStrTrailer(sep);

    // qq
    CONS_RECORD_FIELD_NUM(strRecord, selfQQ_, sep);           // PersonQQ

    // statistics
    CONS_RECORD_FIELD_NUM(strRecord,   c2sPackCount_, sep);   // PersonC2STransPackets
    CONS_RECORD_FIELD_NUM(strRecord,   s2cPackCount_, sep);   // PersonS2CTransPackets
    CONS_RECORD_FIELD_NUM(strRecord,   c2sByteCount_, sep);   // PersonC2STransBytes
    CONS_RECORD_FIELD_NUM(strRecord,   s2cByteCount_, sep);   // PersonS2CTransBytes

    // date
    CONS_RECORD_FIELD_TIME(strRecord,  startTime_, sep);      // PersonStartTime
    CONS_RECORD_FIELD_TIME(strRecord,  LastActiveTime, sep);  // PersonLastActiveTime
    CONS_RECORD_FIELD_TEXT(strRecord,  "", sep);              // PersonResv
    return strRecord;
}

std::string WxcsQQGroupPerson::toStrBlankRecord(char sep)
{
    std::string strRecord;
    // 对应上面字段个数
    for (int i = 0; i < 32; i++)
    {
        strRecord += std::string("\"\"") + sep;
    }

    return strRecord;
}

//////////////////// WxcsQQSinglePerson //////////////////////////////////////////
WxcsQQSinglePerson::WxcsQQSinglePerson()
{
}

WxcsQQSinglePerson::WxcsQQSinglePerson(ST_qqSingle *msg)
    : WxcsPerson((uint8_t*)"", 0
                 , WXCS_SESSION_NONE
                 , msg->srcPort, msg->dstPort
                 , msg->srcIp, msg->dstIp
                 , msg->lastActiveTime
                 , &msg->trailer)
    , selfQQ_(msg->selfQQNum)
    , startTime_(msg->firstActiveTime)
    , c2sPackCount_(msg->c2sPackCount)
    , c2sByteCount_(msg->c2sByteCount)
    , s2cPackCount_(msg->s2cPackCount)
    , s2cByteCount_(msg->s2cByteCount)
    , isTimeout_(msg->isTimeout)
    , sessionType_(msg->sessionType)
{
    qqSession_[0] = msg->qqSession[0];
    qqSession_[1] = msg->qqSession[1];
}

std::string WxcsQQSinglePerson::toStrRecord(char sep) const
{
    // 24 fields
    std::string strRecord;
    strRecord += getStrTrailer(sep);

    // qq
    CONS_RECORD_FIELD_NUM(strRecord, selfQQ_, sep);         // PersonQQ

    // statistics
    CONS_RECORD_FIELD_NUM(strRecord, c2sPackCount_, sep);   // PersonC2STransPackets
    CONS_RECORD_FIELD_NUM(strRecord, s2cPackCount_, sep);   // PersonS2CTransPackets
    CONS_RECORD_FIELD_NUM(strRecord, c2sByteCount_, sep);   // PersonC2STransBytes
    CONS_RECORD_FIELD_NUM(strRecord, s2cByteCount_, sep);   // PersonS2CTransBytes

    // date
    CONS_RECORD_FIELD_TIME(strRecord, startTime_, sep);      // PersonStartTime
    CONS_RECORD_FIELD_TIME(strRecord, LastActiveTime, sep);  // PersonLastActiveTime
    CONS_RECORD_FIELD_TEXT(strRecord, "", sep);              // PersonResv
    return strRecord;
}

std::string WxcsQQSinglePerson::toStrBlankRecord(char sep)
{
    std::string strRecord;
    // 对应上面字段个数
    for (int i = 0; i < 32; i++)
    {
        strRecord += std::string("\"\"") + sep;
    }

    return strRecord;
}

void WxcsQQSinglePerson::setSessionId(const std::string &sessionId)
{
    if (SessionID != sessionId)
        SessionID = sessionId;
}
void WxcsQQSinglePerson::setPublicIP(uint32_t pubIP)
{
  if(cli_pub_ip== 0){
    cli_pub_ip = pubIP;
  }
}

//////////////// WxcsQQFilePerson //////////////////////
WxcsQQFilePerson::WxcsQQFilePerson()
{
}

WxcsQQFilePerson::WxcsQQFilePerson(ST_QQ_File_MSG*p)
    :WxcsPerson((uint8_t*)p->FileEncode, strlen(p->FileEncode),
            p->isGroup,
            p->client_port, p->server_port,
            p->client_ip.ipv4, p->server_ip.ipv4,
            p->LastActiveTime, &p->trailer)
{
    this->isGroup       = p->isGroup;         // 群行为?
    this->isSender      = p->isSender;        // 1:文件发送者, 0:文件为接受者
    this->Filecode      = p->FileEncode;      // 被编码后的文件名
    this->Method        = p->Method;
    this->Host          = p->Host;
    this->URL           = p->URL;
    this->User_Agent    = p->User_Agent;
    this->AcceptEncoding= p->AcceptEncoding;
    this->AcceptLanguage= p->AcceptLanguage;
    this->Connection    = p->Connection;
    this->ContentLength = p->ContentLength;
    this->Cookie        = p->Cookie;
    this->CacheControl  = p->CacheControl;
    this->NetType       = p->NetType;
    this->ispicture     = p->isPicture;
    this->picture_width = p->picture_width;
    this->picture_height= p->picture_height;
    this->QQNum         = p->QQNum;
    this->tblPushFlg    = CFG->GetValueOf<int>("TBL_FILE_PUSH_FLG", 0);

}

std::string WxcsQQFilePerson::toStrRecord(char sep)
{
    // 24 fields
    std::string strRecord;
    strRecord += getStrTrailer(sep);

    CONS_RECORD_FIELD_NUM(strRecord, QQNum,             sep);
    CONS_RECORD_FIELD_NUM (strRecord, isSender,          sep);
    CONS_RECORD_FIELD_TEXT(strRecord, Host,              sep);
    CONS_RECORD_FIELD_TEXT(strRecord, Method,            sep);
    CONS_RECORD_FIELD_TEXT(strRecord, URL,               sep);
    CONS_RECORD_FIELD_TEXT(strRecord, User_Agent,        sep);
    CONS_RECORD_FIELD_TEXT(strRecord, AcceptEncoding,    sep);
    CONS_RECORD_FIELD_TEXT(strRecord, AcceptLanguage,    sep);
    CONS_RECORD_FIELD_TEXT(strRecord, Connection,        sep);
    CONS_RECORD_FIELD_TEXT(strRecord, ContentLength,     sep);
    CONS_RECORD_FIELD_TEXT(strRecord, Cookie,            sep);
    CONS_RECORD_FIELD_TEXT(strRecord, CacheControl,      sep);
    CONS_RECORD_FIELD_TEXT(strRecord, NetType,           sep);
    CONS_RECORD_FIELD_TIME(strRecord, this->LastActiveTime, sep);
    CONS_RECORD_FIELD_TEXT(strRecord, this->QQNumFrom,   sep);

    return strRecord;
}

std::string WxcsQQFilePerson::toStrBlankRecord(char sep)
{
    std::string str;
    // 对应上面字段个数
    for (int i = 0; i < 39; i++)
    {
        str += std::string("\"\"") + sep;
    }
    return str;
}

//////////////////// WxcsSkypePerson //////////////////////////////////////////
WxcsSkypePerson::WxcsSkypePerson()
{
}

WxcsSkypePerson::WxcsSkypePerson(ST_SkypeMediaSessionAlive *p)
    : WxcsPerson(p->SessionID, sizeof(p->SessionID), p->SessionType,
                 p->client_port, p->server_port,
                 p->client_ip.ipv4, p->server_ip.ipv4,
                 p->PersonLastActiveTime,
                 &p->trailer)
{
    C2STransPackets = p->PersonA2BTransPackets;
    S2CTransPackets = p->PersonB2ATransPackets;
    C2STransBytes   = p->PersonA2BTransBytes;
    S2CTransBytes   = p->PersonB2ATransBytes;
    StartTime       = p->PersonFirstActiveTime;
    isTimeout       = p->isTimeout;
}

uint32_t WxcsSkypePerson::getC2STransPackets() const
{
    return C2STransPackets;
}

uint32_t WxcsSkypePerson::getS2CTransPackets() const
{
    return S2CTransPackets;
}

/* 写一个人的完整信息
    注意: 字段个数发生变更时
          toStrBlankRecord 对应的也要修改
*/
std::string WxcsSkypePerson::toStrRecord(char sep) const
{   // 22 fields
    std::string strRecord;
    strRecord += getStrTrailer(sep);  // 建联信息

    // statistics
    CONS_RECORD_FIELD_NUM(strRecord,   C2STransPackets, sep);
    CONS_RECORD_FIELD_NUM(strRecord,   S2CTransPackets, sep);
    CONS_RECORD_FIELD_NUM(strRecord,   C2STransBytes, sep);
    CONS_RECORD_FIELD_NUM(strRecord,   S2CTransBytes, sep);

    // date
    CONS_RECORD_FIELD_TIME(strRecord,  StartTime, sep);
    CONS_RECORD_FIELD_TIME(strRecord,  LastActiveTime, sep);

    //保留字段
    CONS_RECORD_FIELD_TEXT(strRecord,  "", sep);

    return strRecord;
}

// 人数不够时, 写入空的数据, 注意每个人的字段数
std::string WxcsSkypePerson::toStrBlankRecord(char sep)
{
    std::string strRecord;
    for (int i = 0; i < 31; i++)
    {
        strRecord += std::string("\"\"") + sep;
    }

    return strRecord;
}


bool WXRelation::orderInsert(std::string & _clientIP, WxrelaPerInfo & _relaPerInfo)
{
    auto iter = userListOfIP.find(_clientIP);
    std::vector<WxrelaPerInfo> wxrelaPerInfos;
    if (iter != userListOfIP.end()) {
        wxrelaPerInfos = userListOfIP[_clientIP];
        auto find = std::find_if(wxrelaPerInfos.begin(), wxrelaPerInfos.end(), [&](const WxrelaPerInfo& p){
            return _relaPerInfo.timestamp < p.timestamp;
        } );

        if (find != wxrelaPerInfos.end()) {
            // auto index = find - wxrelaPerInfos.begin();
            wxrelaPerInfos.insert(find, _relaPerInfo);
        } else {
            wxrelaPerInfos.push_back(_relaPerInfo);
        }
    } else {
        wxrelaPerInfos.push_back(_relaPerInfo);
        std::pair<std::string, std::vector<WxrelaPerInfo>> info(_clientIP, wxrelaPerInfos);
        userListOfIP.insert(info);
    }

    return true;
}

/*
查找同一个key的wxid列表中离 _clientIp 时间差距最小的元素
*/
std::shared_ptr<WxrelaPerInfo> WXRelation::findNearestPer(std::string & _clientIp, uint32_t _timeStamp)
{
    auto find_ip = this->userListOfIP.find(_clientIp);
    if (find_ip == this->userListOfIP.end()) {
        return nullptr;
    }

    auto userList = this->userListOfIP[_clientIp];
    std::shared_ptr<WxrelaPerInfo> info(new WxrelaPerInfo);
    uint16_t nearest = 0;
    uint16_t index = 0;
    for (int i = 0; i < userList.size(); ++i) {
        uint16_t diff = abs((long)(userList[i].timestamp - _timeStamp));
        if (diff == 0) {
            return std::make_shared<WxrelaPerInfo>(userList[i]);
        }
        if (nearest == 0) {
            nearest = diff;
        }
        if (diff < nearest) {
            index = i;
        }
    }

    return std::make_shared<WxrelaPerInfo>(userList[index]);
}


/*ADD_S by yangna 2020-08-20 */
/*********************************QQevent****************************************************/
WxcsQQEventPerson::WxcsQQEventPerson()
{
    MapQQEvent.clear();
    qqEventlock = PTHREAD_RWLOCK_INITIALIZER;
}


/*向MapQQEvent中插入手机和QQ关系数据*/
int WxcsQQEventPerson::inserQQEventData(ST_QQEventAlive *pQQEvent)
{
    if (NULL == pQQEvent || pQQEvent->trailer.MSISDN <= 0
        || pQQEvent->QQNum <= 0)
    {
        return -1;
    }

    size_t msisdn = pQQEvent->trailer.MSISDN;
    size_t QQNum = pQQEvent->QQNum;
    unsigned int nowTime = 0;
    if (pQQEvent->trailer.TS <= 0)
    {
        nowTime = time(NULL);
    }
    else
    {
        nowTime = pQQEvent->trailer.TS;
    }
    QQEventInfo info;
    info.weight = 1;
    info.lastTime = nowTime;
    info.IMEI = pQQEvent->trailer.IMEI;
    info.IMSI = pQQEvent->trailer.IMSI;

    std::map<size_t, std::map<size_t, QQEventInfo>>::iterator it1 = MapQQEvent.find(msisdn);
    /*先查找手机号码是否存入 */
    if (it1 == MapQQEvent.end())
    {
        /*插入新元素*/
        std::map<size_t, QQEventInfo> mapValueTmp;
        mapValueTmp.emplace(QQNum, info);
        pthread_rwlock_wrlock(&qqEventlock);
        MapQQEvent.emplace(msisdn, mapValueTmp);
        pthread_rwlock_unlock(&qqEventlock);     //解锁
        // printf(" 插入新的手机[%s][%d][msisdn=%zu]\n", __FILE__, __LINE__, msisdn);
    }
    else
    {
        /*手机号已存入，找到手机号--QQ号存储的记录 */
        pthread_rwlock_wrlock(&qqEventlock);
        std::map<size_t, QQEventInfo>::iterator it2 = it1->second.find(QQNum);
        if (it2 ==  it1->second.end())
        {
            /*插入手机号和QQ新的对应关系数据 */
            it1->second.emplace(QQNum, info);
            // printf(" 插入新的QQ[%s][%d][msisdn=%zu][QQNum=%zu]\n", __FILE__, __LINE__, msisdn, QQNum.c_str());
        }
        else
        {  /*更新已插入的QQ的时间和权重 */
            ++it2->second.weight;
            it2->second.lastTime = nowTime;
            it2->second.IMEI = pQQEvent->trailer.IMEI;
            it2->second.IMSI = pQQEvent->trailer.IMSI;
            // printf(" 更新QQ[%s][%d][msisdn=%zu][QQNum=%zu][weight=%d][time=%d]\n",
            // __FILE__, __LINE__, msisdn, QQNum.c_str(), it2->second.first, it2->second.second);
        }
        pthread_rwlock_unlock(&qqEventlock);
    }

    return 0;
}

void WxcsQQEventPerson::loopQQEventData()
{

    std::map<size_t, std::map<size_t, QQEventInfo>>::iterator it1;
    std::map<size_t, QQEventInfo>::iterator it2;
    for (it1 = MapQQEvent.begin(); it1 != MapQQEvent.end(); it1++)
    {
        for (it2 = it1->second.begin(); it2 != it1->second.end(); it2++)
        {
            printf("[%s][%d][msisdn:%zu][QQNum:%zu][weight=%u][time=%u]\n",  __FILE__, __LINE__,
            it1->first, it2->first, it2->second.weight, it2->second.lastTime);
            printf("[%s][%d]=======================================\n",  __FILE__, __LINE__);

        }
        printf("[%s][%d]-------------------------------------\n",  __FILE__, __LINE__);
    }
    // printf("\n\n");
}


/********************************************
 * 函 数 名 : getQQByMsisdnWithWeight
 * 功    能 : 根据手机号码返回权重最大或者最小的QQ号码
 * 传入参数 : msisdb：手机号码
 * 传出参数 : QQNum：查找到的QQ号码， weight权重，lastTime最后更新时间
 * 返 回 值 : 0 查找成功， -1：查找失败
 ********************************************/
int WxcsQQEventPerson::getQQByMsisdnWithWeight(size_t msisdn, size_t &QQNum, unsigned int &weight, unsigned int &lastTime)
{
    if (MapQQEvent.size() <= 0
        || MapQQEvent.find(msisdn) ==  MapQQEvent.end())
    {
        return -1;
    }

    /* 一个手机号码对应一个QQ时，如果QQ的权重小于100不输出 */
    /*一个手机号码对应多个QQ时，如果权重最大的比权重第二大的差值小于200不输出 */
    /*如果有更精确优雅的算法可以替换这个算法 */
    std::map<size_t, QQEventInfo> mapQQEventValue;
    std::map<size_t, QQEventInfo>::iterator it;
    pthread_rwlock_rdlock(&qqEventlock); //读锁
    mapQQEventValue = MapQQEvent.find(msisdn)->second;
    pthread_rwlock_unlock(&qqEventlock);     //解锁

    for (it = mapQQEventValue.begin(); it != mapQQEventValue.end(); it++)
    {
        if (it->second.weight > weight)
        {
            weight = it->second.weight;
            lastTime = it->second.lastTime;
            QQNum = it->first;
        }

    }
    if (weight < 20)
    {
        weight = 0;
        lastTime = 0;
        QQNum = 0;
        return -1;
    }

    return 0;
}


/********************************************
 * 函 数 名 : getQQListByMsisdn
 * 功    能 : 根据手机号码获取对应的所有QQ号码
 * 传入参数 : msisdb：手机号码，
 * 传出参数 : mapQQEventValue：所有QQ
 * 返 回 值 : 0 查找成功， -1：查找失败
 ********************************************/
int WxcsQQEventPerson::getQQListByMsisdn(size_t msisdn, std::map<size_t, QQEventInfo> &mapQQEventValue)
{
    if (MapQQEvent.size() <= 0
        || MapQQEvent.find(msisdn) == MapQQEvent.end())
    {
        return -1;
    }

    pthread_rwlock_rdlock(&qqEventlock); //读锁
    mapQQEventValue = MapQQEvent.find(msisdn)->second;
    pthread_rwlock_unlock(&qqEventlock);     //解锁

    return 0;
}


/********************************************
 * 函 数 名 : getMsisdbByQQWithWeight
 * 功    能 : 根据QQ号码返回权重最大或者最小的手机号码
 * 传入参数 : QQNum：查找到的QQ号码，sortBy：排序标识，true返回最大权重，最新时间，false返回最小权重最旧时间
 * 传出参数 : msisdb：手机号码， weight权重，lastTime最后更新时间
 * 返 回 值 : 0 查找成功， -1：查找失败
 ********************************************/
int WxcsQQEventPerson::getMsisdnByQQWithWeight(size_t QQNum, size_t &msisdn, unsigned int &weight, unsigned int &lastTime)
{
    int ret = -1;
    int flg = 0;
    std::map<size_t, std::map<size_t, QQEventInfo>>::iterator it1;

    pthread_rwlock_rdlock(&qqEventlock); //读锁
    for (it1 = MapQQEvent.begin(); it1 != MapQQEvent.end(); it1++)
    {
        std::map<size_t, QQEventInfo> mapQQEventValue = it1->second;
        if (mapQQEventValue.find(QQNum) != mapQQEventValue.end())
        {
            ret = 0;
            if (flg == 0)
            {
                weight = mapQQEventValue.find(QQNum)->second.weight;
                lastTime = mapQQEventValue.find(QQNum)->second.lastTime;
                msisdn = it1->first;
                flg = 1;
            }

            if (mapQQEventValue.find(QQNum)->second.weight > weight)
            {
                weight = mapQQEventValue.find(QQNum)->second.weight;
                lastTime = mapQQEventValue.find(QQNum)->second.lastTime;
                msisdn = it1->first;
            }
        }
    }
    pthread_rwlock_unlock(&qqEventlock);     //解锁
    // LOG_INTST->debug("[msisdn:{}][QQNum:{}][time={}][weight={}] SessionID:{}",msisdn, QQNum.c_str(), lastTime, weight);
    // printf("[%s][%s][%d][msisdn:%zu][QQNum:%zu][time=%u][weight=%u]\n",  __FILE__, __func__, __LINE__, msisdn, QQNum.c_str(),lastTime, weight);
    return ret;

}

/********************************************
 * 函 数 名 : getAllMsisdnQQ
 * 功    能 : 获取所有的手机号码和QQ对应关系
 * 传入参数 : 无
 * 传出参数 : mapQQEvent 所有手机和QQ
 * 返 回 值 : 0 查找成功， -1：查找失败
 ********************************************/
int WxcsQQEventPerson::getAllMsisdnQQ(std::map<size_t, std::map<size_t, QQEventInfo>> **mapQQEvent)
{
    if (NULL == mapQQEvent)
    {
        return -1;
    }
    *mapQQEvent =  &(this->MapQQEvent);
    return 0;
}
/*********************************QQevent****************************************************/

/*ADD_E by yangna 2020-08-20 */

//////////////////// WxcsTencentMeetingPerson //////////////////////////////////////////
WxcsTencentMeetingPerson::WxcsTencentMeetingPerson()
{
}


WxcsTencentMeetingPerson::WxcsTencentMeetingPerson(ST_TencentMeeting *pMeetingMsg)
    : WxcsPerson((uint8_t*)&pMeetingMsg->sessionId, sizeof(pMeetingMsg->sessionId), 0,
                 pMeetingMsg->srcPort, pMeetingMsg->dstPort,
                 pMeetingMsg->srcIp, pMeetingMsg->dstIp,
                 pMeetingMsg->lastActiveTime,
                 &pMeetingMsg->trailer)
    , sessionId(pMeetingMsg->sessionId)
    , selfLoginNum(pMeetingMsg->selfLoginNum)
    , selfUID(pMeetingMsg->selfUID)
    , firstActiveTime(pMeetingMsg->firstActiveTime)
    , lastActiveTime(pMeetingMsg->lastActiveTime)
    , isTimeout(pMeetingMsg->isTimeout)
{
    if (pMeetingMsg->dstIp6[0] != 0) {
        memcpy(server_ipv6, pMeetingMsg->dstIp6, sizeof(client_ipv6));
    }
    if (pMeetingMsg->srcIp6[0] != 0) {
        memcpy(client_ipv6, pMeetingMsg->srcIp6, sizeof(client_ipv6));
    }
}

std::string WxcsTencentMeetingPerson::toStrRecord(char sep) const
{
    std::string strRecord;
    strRecord += getStrTrailer(sep);  // 建联信息

    // session info
    CONS_RECORD_FIELD_TEXT(strRecord,  selfLoginNum, sep);
    CONS_RECORD_FIELD_TEXT(strRecord,  selfUID, sep);
    // date
    CONS_RECORD_FIELD_TIME(strRecord,  firstActiveTime, sep);
    CONS_RECORD_FIELD_TIME(strRecord,  lastActiveTime, sep);

    //保留字段
    CONS_RECORD_FIELD_TEXT(strRecord,  "", sep);

    return strRecord;
}

std::string WxcsTencentMeetingPerson::toStrBlankRecord(char sep)
{
    std::string strRecord;
    for (int i = 0; i < 31; i++)
    {
        strRecord += std::string("\"\"") + sep;
    }

    return strRecord;
}
